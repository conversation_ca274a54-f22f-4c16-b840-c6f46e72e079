import React, { useState, useEffect } from 'react';
import { Modal, Table, Button, Space, Tag, message, Form, Input, Select } from 'antd';
import { ExclamationCircleOutlined, CheckCircleOutlined, ClockCircleOutlined } from '@ant-design/icons';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import moment from 'moment';
import config from '../../config';
import './ProbationReminder.css';

const { TextArea } = Input;
const { Option } = Select;

const ProbationReminder = ({ visible, onClose, onRefresh }) => {
    const [loading, setLoading] = useState(false);
    const [probationEmployees, setProbationEmployees] = useState([]);
    const [confirmModalVisible, setConfirmModalVisible] = useState(false);
    const [selectedEmployee, setSelectedEmployee] = useState(null);
    const [form] = Form.useForm();
    const [isSubmitting, setIsSubmitting] = useState(false); // 防止重复提交

    // 解析日期字符串为Date对象
    const parseDate = (dateString) => {
        if (!dateString) return null;
        const date = new Date(dateString);
        return isNaN(date.getTime()) ? null : date;
    };

    // 处理手动输入日期的实时匹配
    const handleDateInputMatch = (value, fieldName) => {
        // 尝试匹配年份
        if (value.length >= 4) {
            const yearStr = value.substring(0, 4);
            const year = parseInt(yearStr, 10);

            // 检查是否是有效年份
            if (!isNaN(year) && year >= 1900 && year <= 2100) {
                let month = 1;
                let day = 1;

                // 检查是否有分隔符
                const hasSeparator = value.length > 4 && (value[4] === '-' || value[4] === '/');

                // 尝试匹配月份 (处理 YYYY-M, YYYY-MM, YYYY/M, YYYY/MM 或 YYYYMM 格式)
                if (value.length >= 5) {
                    let monthStr;

                    if (hasSeparator) {
                        // 处理带分隔符的格式 (YYYY-MM 或 YYYY/MM)
                        if (value.length >= 6) {
                            monthStr = value.substring(5);
                            // 如果有第二个分隔符，只取分隔符前的部分
                            const secondSeparatorIndex = monthStr.indexOf('-') !== -1 ?
                                monthStr.indexOf('-') : monthStr.indexOf('/');
                            if (secondSeparatorIndex !== -1) {
                                monthStr = monthStr.substring(0, secondSeparatorIndex);
                            }
                        }
                    } else {
                        // 处理无分隔符的格式 (YYYYMM)
                        monthStr = value.substring(4);
                        if (monthStr.length > 2) {
                            monthStr = monthStr.substring(0, 2);
                        }
                    }

                    if (monthStr && monthStr.length > 0) {
                        month = parseInt(monthStr, 10);
                        if (isNaN(month) || month < 1 || month > 12) {
                            month = 1;
                        }
                    }

                    // 尝试匹配日期
                    if (hasSeparator) {
                        // 查找第二个分隔符
                        const secondSeparatorIndex = value.indexOf('-', 5) !== -1 ?
                            value.indexOf('-', 5) : value.indexOf('/', 5);

                        if (secondSeparatorIndex !== -1 && value.length > secondSeparatorIndex) {
                            // 有第二个分隔符，提取日期部分
                            const dayStr = value.substring(secondSeparatorIndex + 1);
                            if (dayStr.length > 0) {
                                day = parseInt(dayStr, 10);
                                const daysInMonth = new Date(year, month, 0).getDate();
                                if (isNaN(day) || day < 1 || day > daysInMonth) {
                                    day = 1;
                                }
                            }
                        }
                    } else if (value.length >= 6) {
                        // 处理无分隔符的格式 (YYYYMM 或 YYYYMMDD)
                        if (value.length >= 8) {
                            // 完整的 YYYYMMDD 格式
                            const dayStr = value.substring(6);
                            if (dayStr.length > 0) {
                                day = parseInt(dayStr, 10);
                                const daysInMonth = new Date(year, month, 0).getDate();
                                if (isNaN(day) || day < 1 || day > daysInMonth) {
                                    day = 1;
                                }
                            }
                        } else if (value.length === 6 || value.length === 7) {
                            // YYYYMM 格式，日期默认为1
                            day = 1;
                        }
                    }
                }

                // 创建日期对象并更新表单
                const date = moment([year, month - 1, day]);

                // 格式化日期为 YYYY-MM-DD 格式
                const formattedDate = date.format('YYYY-MM-DD');

                // 更新表单字段
                form.setFieldsValue({ [fieldName]: moment(formattedDate) });
            }
        }
    };


    // 岗位选项（与员工表单保持一致）
    const positionOptions = [
        { value: '行政管理', label: '行政管理' },
        { value: '技术工程师', label: '技术工程师' },
        { value: '项目经理', label: '项目经理' },
        { value: '项目代表', label: '项目代表' },
        { value: '项目助理', label: '项目助理' },
        { value: '项目管理', label: '项目管理' },
        { value: '业务经理', label: '业务经理' },
        { value: '品控工程师', label: '品控工程师' },
        { value: '采购工程师', label: '采购工程师' },
        { value: '采购助理', label: '采购助理' },
        { value: '外贸专员', label: '外贸专员' },
        { value: '出纳/会计', label: '出纳/会计' },
        { value: '行政助理', label: '行政助理' },
        { value: '其他', label: '其他' }
    ];

    // 部门选项（与员工表单保持一致）
    const departments = [
        {
            value: '工程部',
            label: '工程部',
            subOptions: [
                { value: '技术科', label: '技术科' },
                { value: '项目管理科', label: '项目管理科' }
            ]
        },
        {
            value: '商贸部',
            label: '商贸部',
        },
        {
            value: '供应管理部',
            label: '供应管理部',
        },
        {
            value: '财务部',
            label: '财务部'
        },
        {
            value: '行政部',
            label: '行政部',
            subOptions: [
                { value: '行政管理', label: '行政管理' },
                { value: '综合办', label: '综合办' }
            ]
        }
    ];

    // 生成部门选项列表（包含子部门）
    const getDepartmentOptions = () => {
        const options = [];
        departments.forEach(dept => {
            options.push({ value: dept.value, label: dept.label });
            if (dept.subOptions) {
                dept.subOptions.forEach(subDept => {
                    options.push({
                        value: `${dept.value}-${subDept.value}`,
                        label: `${dept.label} - ${subDept.label}`
                    });
                });
            }
        });
        return options;
    };

    // 获取即将到期的试用期员工
    const fetchProbationEmployees = async () => {
        setLoading(true);
        try {
            const response = await fetch(`${config.apiBaseUrl}/employees/probation-reminders`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('获取试用期员工数据失败');
            }

            const data = await response.json();
            setProbationEmployees(data);
        } catch (error) {
            console.error('获取试用期员工失败:', error);
            message.error('获取试用期员工数据失败');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (visible) {
            fetchProbationEmployees();
        }
    }, [visible]);

    // 组件卸载时清理状态
    useEffect(() => {
        return () => {
            // 清理所有状态，防止内存泄漏
            setConfirmModalVisible(false);
            setSelectedEmployee(null);
            setIsSubmitting(false);
            form.resetFields();
        };
    }, [form]);

    // 计算剩余天数
    const calculateRemainingDays = (endDate) => {
        const today = moment();
        const end = moment(endDate);
        return end.diff(today, 'days');
    };

    // 获取状态标签
    const getStatusTag = (employee) => {
        const remainingDays = calculateRemainingDays(employee.probationEndDate);
        
        if (remainingDays < 0) {
            return <Tag color="red" icon={<ExclamationCircleOutlined />}>已超期 {Math.abs(remainingDays)} 天</Tag>;
        } else if (remainingDays === 0) {
            return <Tag color="orange" icon={<ClockCircleOutlined />}>今日到期</Tag>;
        } else if (remainingDays <= 7) {
            return <Tag color="gold" icon={<ClockCircleOutlined />}>还有 {remainingDays} 天到期</Tag>;
        } else {
            return <Tag color="blue">还有 {remainingDays} 天</Tag>;
        }
    };

    // 处理转正确认
    const handleConfirmRegularization = (employee) => {
        setSelectedEmployee(employee);

        // 处理部门显示值
        let departmentValue = employee.department;
        if (employee.subDepartment) {
            departmentValue = `${employee.department}-${employee.subDepartment}`;
        }

        form.setFieldsValue({
            regularizationDate: moment(),
            newPosition: employee.position || '',
            newDepartment: departmentValue,
            remarks: ''
        });
        setConfirmModalVisible(true);
    };

    // 处理取消转正确认
    const handleCancelRegularization = () => {
        try {
            // 防止在loading状态下关闭
            if (loading) {
                return;
            }

            // 立即关闭弹窗，避免用户重复点击
            setConfirmModalVisible(false);

            // 延迟重置其他状态，确保弹窗完全关闭
            setTimeout(() => {
                form.resetFields();
                setSelectedEmployee(null);
            }, 100);
        } catch (error) {
            console.error('关闭弹窗时出错:', error);
            // 强制重置所有状态
            setConfirmModalVisible(false);
            setSelectedEmployee(null);
            form.resetFields();
        }
    };

    // 提交转正确认
    const submitRegularization = async () => {
        // 防止重复提交
        if (isSubmitting || loading) {
            return;
        }

        try {
            const values = await form.validateFields();
            setIsSubmitting(true);
            setLoading(true);

            // 解析部门和子部门
            let newDepartment = values.newDepartment;
            let newSubDepartment = '';

            if (values.newDepartment && values.newDepartment.includes('-')) {
                const parts = values.newDepartment.split('-');
                newDepartment = parts[0];
                newSubDepartment = parts[1];
            }

            // 添加超时处理
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时

            try {
                const response = await fetch(`${config.apiBaseUrl}/employees/${selectedEmployee._id}/regularization`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        regularizationDate: values.regularizationDate.format('YYYY-MM-DD'),
                        newPosition: values.newPosition,
                        newDepartment: newDepartment,
                        newSubDepartment: newSubDepartment,
                        remarks: values.remarks
                    }),
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.message || '转正确认失败');
                }

                message.success(`${selectedEmployee.name} 转正确认成功！`);

                // 先关闭弹窗
                setConfirmModalVisible(false);

                // 延迟重置状态，确保弹窗完全关闭
                setTimeout(() => {
                    form.resetFields();
                    setSelectedEmployee(null);
                }, 100);

                // 刷新数据 - 使用 setTimeout 避免与状态重置冲突
                setTimeout(async () => {
                    try {
                        await fetchProbationEmployees();
                        if (onRefresh) {
                            onRefresh();
                        }
                    } catch (refreshError) {
                        console.error('刷新数据失败:', refreshError);
                    }
                }, 200);
            } catch (fetchError) {
                clearTimeout(timeoutId);
                if (fetchError.name === 'AbortError') {
                    throw new Error('请求超时，请检查网络连接后重试');
                }
                throw fetchError;
            }
        } catch (error) {
            console.error('转正确认失败:', error);
            if (error.message.includes('超时')) {
                message.error('操作超时，请检查网络连接后重试');
            } else {
                message.error(error.message || '转正确认失败，请重试');
            }
        } finally {
            setLoading(false);
            setIsSubmitting(false);
        }
    };

    const columns = [
        {
            title: '员工编号',
            dataIndex: 'employeeId',
            key: 'employeeId',
            width: 80,
            fixed: 'left'
        },
        {
            title: '姓名',
            dataIndex: 'name',
            key: 'name',
            width: 80,
            fixed: 'left'
        },
        {
            title: '部门',
            dataIndex: 'department',
            key: 'department',
            width: 130,
            render: (text, record) => {
                if (record.subDepartment) {
                    return `${text} - ${record.subDepartment}`;
                }
                return text;
            }
        },
        {
            title: '岗位',
            dataIndex: 'position',
            key: 'position',
            width: 90
        },
        {
            title: '入职日期',
            dataIndex: 'entryDate',
            key: 'entryDate',
            width: 100
        },
        {
            title: '试用期截止',
            dataIndex: 'probationEndDate',
            key: 'probationEndDate',
            width: 100
        },
        {
            title: '状态',
            key: 'status',
            width: 130,
            render: (_, employee) => getStatusTag(employee)
        },
        {
            title: '操作',
            key: 'actions',
            width: 100,
            fixed: 'right',
            render: (_, employee) => (
                <Space>
                    <Button
                        type="primary"
                        size="small"
                        icon={<CheckCircleOutlined />}
                        onClick={() => handleConfirmRegularization(employee)}
                    >
                        确认转正
                    </Button>
                </Space>
            )
        }
    ];

    return (
        <>
            <Modal
                title={
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                        <ExclamationCircleOutlined style={{ color: '#faad14', marginRight: 8 }} />
                        试用期到期提醒
                    </div>
                }
                open={visible}
                onCancel={onClose}
                footer={[
                    <Button key="close" onClick={onClose} className="probation-reminder-close-btn">
                        关闭
                    </Button>
                ]}
                width={800}
                destroyOnClose
                className="probation-reminder-modal"
            >
                <div style={{ marginBottom: 16 }}>
                    <p style={{ color: '#666', margin: 0 }}>
                        以下员工的试用期即将到期或已超期，请及时处理转正事宜：
                    </p>
                </div>
                
                <Table
                    columns={columns}
                    dataSource={probationEmployees}
                    rowKey="_id"
                    loading={loading}
                    pagination={false}
                    scroll={{ x: 800, y: 400 }}
                    size="small"
                />
                
                {probationEmployees.length === 0 && !loading && (
                    <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
                        <CheckCircleOutlined style={{ fontSize: 48, marginBottom: 16 }} />
                        <p>暂无即将到期的试用期员工</p>
                    </div>
                )}
            </Modal>

            {/* 转正确认弹窗 */}
            <Modal
                title="确认员工转正"
                open={confirmModalVisible}
                onCancel={handleCancelRegularization}
                width={600}
                className="probation-regularization-modal"
                maskClosable={!loading && !isSubmitting}
                closable={!loading && !isSubmitting}
                destroyOnClose={true}
                forceRender={false}
                footer={
                    <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '8px' }}>
                        <Button
                            onClick={handleCancelRegularization}
                            disabled={loading || isSubmitting}
                        >
                            取消
                        </Button>
                        <Button
                            type="primary"
                            onClick={submitRegularization}
                            loading={loading || isSubmitting}
                            disabled={isSubmitting}
                        >
                            确认转正
                        </Button>
                    </div>
                }
            >
                {selectedEmployee && (
                    <Form
                        form={form}
                        layout="vertical"
                    >
                        <div style={{ marginBottom: 16, padding: 12, backgroundColor: '#f5f5f5', borderRadius: 4 }}>
                            <p><strong>员工信息：</strong>{selectedEmployee.name} ({selectedEmployee.employeeId})</p>
                            <p><strong>当前部门：</strong>{selectedEmployee.department}</p>
                            <p><strong>当前岗位：</strong>{selectedEmployee.position}</p>
                            <p><strong>试用期截止：</strong>{selectedEmployee.probationEndDate}</p>
                        </div>

                        <div style={{ display: 'flex', gap: '16px' }}>
                            <Form.Item
                                name="regularizationDate"
                                label="转正日期"
                                rules={[{ required: true, message: '请选择转正日期' }]}
                                style={{ flex: 1 }}
                            >
                                <div style={{ position: 'relative' }}>
                                    <DatePicker
                                        selected={form.getFieldValue('regularizationDate') ? moment(form.getFieldValue('regularizationDate')).toDate() : null}
                                        onChange={date => form.setFieldsValue({ regularizationDate: date ? moment(date) : null })}
                                        dateFormat="yyyy-MM-dd"
                                        placeholderText="请选择日期"
                                        className="form-control"
                                        shouldCloseOnSelect={true}
                                        showPopperArrow={false}
                                        onClickOutside={() => {}}
                                        onChangeRaw={(e) => {
                                            const value = e.target.value;
                                            if (value) {
                                                handleDateInputMatch(value, 'regularizationDate');
                                            }
                                        }}
                                        onKeyDown={(e) => {
                                            if (e.key === 'Enter') {
                                                e.preventDefault();
                                                const value = e.target.value;
                                                if (value) {
                                                    handleDateInputMatch(value, 'regularizationDate');
                                                }
                                            }
                                        }}
                                        popperProps={{
                                            positionFixed: true,
                                            modifiers: {
                                                preventOverflow: {
                                                    enabled: true,
                                                    escapeWithReference: false,
                                                    boundariesElement: 'viewport'
                                                }
                                            }
                                        }}
                                    />
                                </div>
                            </Form.Item>

                            <Form.Item
                                name="newPosition"
                                label="转正后岗位"
                                rules={[{ required: true, message: '请选择转正后岗位' }]}
                                style={{ flex: 1 }}
                            >
                                <Select placeholder="请选择转正后岗位" showSearch>
                                    {positionOptions.map(option => (
                                        <Option key={option.value} value={option.value}>
                                            {option.label}
                                        </Option>
                                    ))}
                                </Select>
                            </Form.Item>

                            <Form.Item
                                name="newDepartment"
                                label="转正后部门"
                                rules={[{ required: true, message: '请选择转正后部门' }]}
                                style={{ flex: 1 }}
                            >
                                <Select placeholder="请选择转正后部门" showSearch>
                                    {getDepartmentOptions().map(option => (
                                        <Option key={option.value} value={option.value}>
                                            {option.label}
                                        </Option>
                                    ))}
                                </Select>
                            </Form.Item>
                        </div>

                        <Form.Item
                            name="remarks"
                            label="备注"
                        >
                            <TextArea
                                rows={3}
                                placeholder="请输入转正备注信息（可选）"
                                maxLength={200}
                                showCount={false}
                            />
                            <div style={{ textAlign: 'right', color: '#999', fontSize: '12px', marginTop: '4px' }}>
                                最多200个字符
                            </div>
                        </Form.Item>
                    </Form>
                )}
            </Modal>
        </>
    );
};

export default ProbationReminder;
