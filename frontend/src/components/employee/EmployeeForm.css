.form-header {
    margin-bottom: 30px;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 20px;
}

.form-header h2 {
    color: #2c3e50;
    font-size: 24px;
    margin: 0;
}

.employee-form {
    display: flex;
    flex-direction: column;  /* 改为纵向排列 */
    gap: 24px;
}

.employee-form-container {
    max-width: 100%;
    margin: 0 auto;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.form-section h3 {
    margin-bottom: 20px;
    color: #333;
    border-bottom: 2px solid #2196f3;
    padding-bottom: 10px;
}

.form-row {
    display: flex;
    gap: 20px;
    width: 100%;
}

/* 第一行布局（姓名、性别、民族、血型） */
.form-row.basic-info {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 20px;
}

/* 第二行布局（身份证和出生日期） */
.form-row:not(.basic-info):not(.emergency-info) {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

/* 学历信息行布局 */
.form-row.education-info {
    display: grid;
    grid-template-columns: 1fr 1fr 2fr;
    gap: 20px;
}

.form-row.education-detail {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
}

/* 全宽行（家庭住址和常用药物） */
.form-group.full-width {
    width: 100%;
}

/* 紧急联系人信息行 */
.form-row.emergency-info {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
    margin-top: 24px;
    padding: 20px;
}

.form-actions button {
    display: inline-block;
    text-align: center;
    height: 32px;
    line-height: 32px;
    padding: 0 15px;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    color: white;
    white-space: nowrap;
    min-width: 90px;
    width: auto;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 返回按钮 - 蓝色 */
.back-button {
    background-color: #3b82f6;
}

.back-button:hover {
    background-color: #2563eb;
}

/* 重置按钮 - 橙色 */
.reset-button {
    background-color: #f59e0b;
}

.reset-button:hover {
    background-color: #d97706;
}

/* 保存按钮 - 绿色 */
.submit-button {
    background-color: #10b981;
}

.submit-button:hover {
    background-color: #059669;
}

/* 自动填充按钮 - 紫色 */
.auto-fill-button {
    background-color: #8b5cf6;
}

.auto-fill-button:hover {
    background-color: #7c3aed;
}
/* 继续按钮 - 青色 */
.continue-button {
    background-color: #06b6d4;
}

.continue-button:hover {
    background-color: #0891b2;
}

/* 全局样式 - 确保所有表单元素使用小字体 - 使用更具体的选择器和 !important */
.employee-form-container .ant-input,
.employee-form-container .ant-select,
.employee-form-container .ant-select-selector,
.employee-form-container .ant-select-selection-item,
.employee-form-container .ant-picker,
.employee-form-container .ant-picker-input,
.employee-form-container .ant-picker-input > input,
.employee-form-container .ant-picker-suffix,
.employee-form-container .ant-select-dropdown,
.employee-form-container .ant-select-item,
.employee-form-container .ant-select-item-option-content,
.employee-form-container .ant-picker-dropdown,
.employee-form-container .ant-picker-cell-inner,
.employee-form-container .ant-picker-header,
.employee-form-container .ant-picker-content th,
.employee-form-container .ant-picker-content td,
.employee-form-container .ant-picker-time-panel-column > li,
.employee-form-container .ant-picker-ranges {
    font-size: 12px !important;
}

/* 覆盖 Ant Design 的默认样式 */
body .employee-form-container .ant-select-dropdown .ant-select-item {
    font-size: 12px !important;
}

body .employee-form-container .ant-picker-dropdown .ant-picker-cell-inner {
    font-size: 12px !important;
}

/* 使用更高特异性的选择器 */
html body .employee-form-container .ant-input,
html body .employee-form-container .ant-select-selector,
html body .employee-form-container .ant-picker,
html body .employee-form-container .ant-picker-input > input {
    font-size: 12px !important;
}

/* 统一输入框样式 - 使用更具体的选择器和 !important */
.form-group input,
.form-group select,
.form-group textarea,
.form-group .ant-input,
.form-group .ant-select-selector,
.form-group .ant-picker,
.form-group .ant-picker-input > input {
    width: 100%;
    height: 32px;
    padding: 4px 8px;
    font-size: 12px !important;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    box-sizing: border-box;
}

/* 确保日期选择器内的文字也是小字体 - 使用更具体的选择器和 !important */
body .ant-picker-input > input,
body .ant-picker-suffix,
body .ant-select-selection-item {
    font-size: 12px !important;
}

/* 文本域特殊处理 */
.form-group textarea {
    height: 60px;
    resize: vertical;
}

/* 标签文字统一 */
.form-group label {
    font-size: 13px;
    margin-bottom: 4px;
    display: block;
}

/* 只读输入框样式 */
.readonly-input {
    background-color: #f5f5f5;
    cursor: not-allowed;
}

/* 自定义日期输入组件样式 */
.custom-date-input {
    position: relative;
    width: 100%;
}

.custom-date-input input {
    width: 100%;
    padding-right: 60px; /* 为下拉菜单留出空间 */
}

.date-type-select {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    border: none;
    background: transparent;
    width: 60px;
    font-size: 12px;
    text-align: center;
    cursor: pointer;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23666' d='M2 4l4 4 4-4'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 8px center;
    padding-right: 20px;
}

.date-type-select:focus {
    outline: none;
}

/* 下拉菜单选项样式 - 使用更具体的选择器和 !important */
.form-group select option {
    font-size: 12px !important;
    padding: 4px 8px;
}

/* 使用更高特异性的选择器和 !important */
body .ant-select-item,
body .ant-select-dropdown .ant-select-item-option-content,
body .ant-picker-dropdown .ant-picker-cell-inner {
    font-size: 12px !important;
    padding: 4px 8px;
}

/* 部门选择相关样式 */
.department-select {
    position: relative;
    width: 100%;
}

.department-select .custom-select {
    padding: 4px 8px;
    font-size: 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    background-color: white;
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23666' d='M2 4l4 4 4-4'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 8px center;
    padding-right: 24px;
    line-height: 24px;
}

.department-menu {
    position: absolute;
    top: calc(100% + 4px);
    left: 0;
    width: 100%;
    background-color: white;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    z-index: 1000;
}

.department-option {
    position: relative;  /* 添加相对定位 */
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.2s;
    font-size: 12px;
    pointer-events: auto;  /* 确保可以接收点击事件 */
    -webkit-user-select: none;  /* 防止文本选择干扰点击 (Safari) */
    user-select: none;          /* 防止文本选择干扰点击 */
}

.department-option:hover {
    background-color: #f5f5f5;
}

.sub-departments {
    position: absolute;
    left: 100%;
    top: 0;
    min-width: 120px;
    background-color: white;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    box-shadow: 2px 2px 8px rgba(0,0,0,0.15);
    display: none;  /* 默认隐藏 */
    z-index: 1001;  /* 确保子菜单在最上层 */
}

.department-option:hover > .sub-departments {
    display: block !important;  /* hover 时显示子菜单，使用 !important 确保优先级 */
}

.department-option .sub-departments .department-option {
    border-bottom: 1px solid #f0f0f0;
    position: relative;  /* 确保子选项可以正常点击 */
}

.department-option .sub-departments .department-option:last-child {
    border-bottom: none;
}

.department-option .sub-departments .department-option:hover {
    background-color: #e6f7ff;  /* 子选项hover时使用不同颜色 */
}

/* 职称级别下拉选择样式 */
.title-levels-dropdown {
    position: relative;
    width: 100%;
}

.title-levels-selector {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 4px 8px;
    background: white;
    min-height: 32px;
    max-height: 80px;
    cursor: pointer;
    transition: border-color 0.2s ease;
    box-sizing: border-box;
    overflow: hidden;
}

.title-levels-selector:hover {
    border-color: #1890ff;
}

.selected-titles-display {
    flex: 1;
    overflow: hidden;
    word-wrap: break-word;
    word-break: break-all;
}

.selected-titles-display .placeholder {
    color: #999;
    font-style: italic;
    font-size: 12px;
    line-height: 1.4;
}

.selected-titles-display .selected-text {
    color: #333;
    font-weight: 500;
    font-size: 12px;
    line-height: 1.3;
    display: block;
    word-wrap: break-word;
    word-break: break-all;
    white-space: normal;
}

/* 自适应字体大小和行高 */
.selected-titles-display .selected-text.long-content {
    font-size: 11px;
    line-height: 1.2;
}

.selected-titles-display .selected-text.very-long-content {
    font-size: 10px;
    line-height: 1.1;
}

/* 当内容超长时的处理 */
.selected-titles-display .selected-text.extremely-long-content {
    font-size: 9px;
    line-height: 1.0;
    max-height: 72px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #ccc transparent;
}

.selected-titles-display .selected-text.extremely-long-content::-webkit-scrollbar {
    width: 4px;
}

.selected-titles-display .selected-text.extremely-long-content::-webkit-scrollbar-track {
    background: transparent;
}

.selected-titles-display .selected-text.extremely-long-content::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 2px;
}

.selected-titles-display .selected-text.extremely-long-content::-webkit-scrollbar-thumb:hover {
    background: #999;
}

.dropdown-arrow {
    color: #999;
    font-size: 12px;
    transition: transform 0.2s ease;
    margin-top: 2px;
    flex-shrink: 0;
}

.title-levels-selector:hover .dropdown-arrow {
    color: #1890ff;
}

/* 下拉菜单 */
.title-levels-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    max-height: 200px;
    overflow-y: auto;
}

.title-level-option {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid #f0f0f0;
    font-size: 12px;
}

.title-level-option:last-child {
    border-bottom: none;
}

.title-level-option:hover {
    background-color: #f5f7fa;
}

.title-level-option.selected {
    background-color: #e6f7ff;
    color: #1890ff;
}

.option-indicator {
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    font-size: 12px;
    font-weight: bold;
    color: #1890ff;
}

.option-indicator.radio {
    border-radius: 50%;
    border: 1px solid #d9d9d9;
    background: white;
}

.option-indicator.checkbox {
    border-radius: 2px;
    border: 1px solid #d9d9d9;
    background: white;
}

.title-level-option.selected .option-indicator.radio {
    border-color: #1890ff;
    background: #1890ff;
    color: white;
}

.title-level-option.selected .option-indicator.checkbox {
    border-color: #1890ff;
    background: #1890ff;
    color: white;
}

.option-text {
    font-size: 12px;
}

/* 已选择的执业资格显示 */
.selected-qualifications {
    padding: 8px 0;
    border-bottom: 1px solid #e8e8e8;
    margin-bottom: 8px;
}

.selected-qualifications-title {
    font-size: 12px;
    color: #666;
    margin-bottom: 6px;
    font-weight: 500;
}

.selected-qualification-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px 8px;
    margin: 2px 0;
    background-color: #f0f8ff;
    border: 1px solid #d0e7ff;
    border-radius: 4px;
    font-size: 13px;
}

.qualification-text {
    color: #1890ff;
    font-weight: 500;
}

.remove-qualification {
    color: #ff4d4f;
    cursor: pointer;
    font-weight: bold;
    font-size: 16px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.remove-qualification:hover {
    background-color: #ff4d4f;
    color: white;
}

.qualification-separator {
    height: 1px;
    background-color: #e8e8e8;
    margin: 8px 0;
}

.add-qualification-title {
    font-size: 12px;
    color: #666;
    margin-bottom: 6px;
    font-weight: 500;
}

/* 职称级别下拉菜单样式 */
.qualification-select {
    position: relative;
    width: 100%;
}

.qualification-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    width: 200px;
    z-index: 1000;
    margin-top: 4px;
}

.qualification-option {
    padding: 8px 12px;
    cursor: pointer;
    position: relative;
    font-size: 12px;
}

.qualification-option:hover {
    background-color: #f5f7fa;
}

.qualification-option:hover > .qualification-submenu,
.qualification-submenu:hover,
.option-item:hover > .qualification-submenu {
    display: block;
}

.option-item {
    padding: 8px 12px;
    cursor: pointer;
    white-space: nowrap;
    position: relative;
    font-size: 12px;
}

.option-item:hover {
    background-color: #f5f7fa;
}

/* 保留这个更完整的定义 */
.form-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 15px;
    width: 100%;
    box-sizing: border-box;
}

.qualification-submenu {
    display: none;
    position: absolute;
    left: 100%;
    top: 0;
    background: white;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    min-width: 150px;
}

.qualification-option:hover > .qualification-submenu {
    display: block;
}

@media print {
    .form-actions {
        display: none !important;
    }

    .employee-form-container {
        padding: 0 !important;
        box-shadow: none !important;
        margin: 0 !important;
    }

    .form-section {
        page-break-inside: avoid;
        border: none !important;
        padding: 10px 0 !important;
    }

    .qualification-menu,
    .qualification-submenu {
        display: none !important;
    }

    /* 优化打印时的表单布局 */
    .form-content {
        gap: 10px !important;
    }

    .form-row {
        gap: 10px !important;
    }

    /* 确保文本清晰可见 */
    .form-group input,
    .form-group select,
    .form-group textarea {
        border-color: #000 !important;
        color: #000 !important;
    }

    /* 优化标题样式 */
    .form-section h3 {
        color: #000 !important;
        border-bottom-color: #000 !important;
    }
}

.export-button {
    background-color: #2563eb;
}

.export-button:hover {
    background-color: #1d4ed8;
}

.print-button {
    background-color: #10b981;
}

.print-button:hover {
    background-color: #059669;
}

/* 员工详情打印样式 */
.employee-detail-print {
    padding: 20px;
    font-family: 'SimSun', 'Arial', sans-serif;
    color: #000;
    background-color: #fff;
}

.employee-detail-print h2 {
    font-size: 182px;
    font-weight: bold;
    margin-bottom: 20px;
    text-align: center;
}

.employee-detail-print h3 {
    font-size: 13px;
    font-weight: bold;
    margin: 15px 0 10px;
    border-bottom: 1px solid #333;
    padding-bottom: 5px;
}

.employee-detail-print .print-section {
    margin-bottom: 20px;
}

.employee-detail-print .print-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 15px;
}

.employee-detail-print .print-table td {
    padding: 8px;
    border: 1px solid #ddd;
}

.employee-detail-print .print-table td.label {
    font-weight: bold;
    background-color: #f5f5f5;
    width: 15%;
}

.employee-detail-print .print-footer {
    margin-top: 30px;
    text-align: right;
    font-size: 13px;
    color: #666;
}

@media print {
    .employee-detail-print {
        padding: 0;
        margin: 0;
    }

    .employee-detail-print h2 {
        font-size: 18px;
    }

    .employee-detail-print h3 {
        font-size: 13px;
    }

    .employee-detail-print .print-table td {
        padding: 5px;
        font-size: 13px;
    }

    .employee-detail-print .print-footer {
        margin-top: 20px;
        font-size: 13px;
    }

    /* 确保表格内容不会被分页断开 */
    .employee-detail-print .print-section {
        page-break-inside: avoid;
    }
}
