/* 试用期提醒弹窗样式 */

/* 主弹窗样式 */
.probation-reminder-modal .ant-modal {
  width: 980px !important;
  max-width: 95vw !important;
}

.probation-reminder-modal .ant-modal-content {
  width: 980px !important;
  max-width: 95vw !important;
}

.probation-reminder-modal .ant-modal-body {
  padding: 24px !important;
  max-height: 80vh !important;
  overflow-y: auto !important;
}

/* 表格样式优化 */
.probation-reminder-modal .ant-table {
  font-size: 14px !important;
}

.probation-reminder-modal .ant-table-thead > tr > th {
  background-color: #fafafa !important;
  font-weight: 600 !important;
  text-align: center !important;
  padding: 12px 8px !important;
  white-space: nowrap !important;
}

.probation-reminder-modal .ant-table-tbody > tr > td {
  padding: 12px 8px !important;
  text-align: center !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 状态标签样式 */
.probation-reminder-modal .ant-tag {
  margin: 0 !important;
  padding: 4px 8px !important;
  border-radius: 4px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  white-space: nowrap !important;
}

/* 操作按钮样式 */
.probation-reminder-modal .ant-btn-primary {
  background-color: #1890ff !important;
  border-color: #1890ff !important;
  height: 28px !important;
  padding: 0 12px !important;
  font-size: 12px !important;
  border-radius: 4px !important;
}

.probation-reminder-modal .ant-btn-primary:hover {
  background-color: #40a9ff !important;
  border-color: #40a9ff !important;
}

/* 底部按钮样式 */
.probation-reminder-modal .ant-modal-footer {
  text-align: center !important;
  padding: 16px 24px !important;
  border-top: 1px solid #f0f0f0 !important;
}

.probation-reminder-close-btn {
  min-width: 80px !important;
  height: 36px !important;
  padding: 0 16px !important;
  font-size: 14px !important;
  border-radius: 4px !important;
  background-color: #fff !important;
  border-color: #d9d9d9 !important;
  color: rgba(0, 0, 0, 0.65) !important;
}

.probation-reminder-close-btn:hover {
  border-color: #40a9ff !important;
  color: #40a9ff !important;
}

/* 转正确认弹窗样式 */
.probation-regularization-modal .ant-modal {
  width: 600px !important;
  max-width: 90vw !important;
}

.probation-regularization-modal .ant-modal-content {
  width: 600px !important;
  max-width: 90vw !important;
}

.probation-regularization-modal .ant-modal-body {
  padding: 24px !important;
}

/* 员工信息展示区域 */
.probation-regularization-modal .ant-modal-body > div:first-child {
  margin-bottom: 16px !important;
  padding: 12px !important;
  background-color: #f5f5f5 !important;
  border-radius: 4px !important;
}

.probation-regularization-modal .ant-modal-body > div:first-child p {
  margin: 4px 0 !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
}

/* 表单样式 */
.probation-regularization-modal .ant-form-item {
  margin-bottom: 20px !important;
}

.probation-regularization-modal .ant-form-item-label {
  padding-bottom: 4px !important;
}

.probation-regularization-modal .ant-form-item-label > label {
  font-size: 14px !important;
  font-weight: 500 !important;
  color: #333 !important;
}

/* 一行三列布局样式 */
.probation-regularization-modal .ant-form-item[style*="flex: 1"] {
  margin-bottom: 20px !important;
}

.probation-regularization-modal .ant-form-item[style*="flex: 1"] .ant-form-item-label {
  white-space: nowrap !important;
}

.probation-regularization-modal .ant-input,
.probation-regularization-modal .ant-select-selector,
.probation-regularization-modal .ant-picker {
  height: 36px !important;
  border-radius: 4px !important;
  font-size: 14px !important;
  border: 1px solid #d9d9d9 !important;
  transition: all 0.3s ease !important;
}

.probation-regularization-modal .ant-input:focus,
.probation-regularization-modal .ant-select-focused .ant-select-selector,
.probation-regularization-modal .ant-picker:hover,
.probation-regularization-modal .ant-picker-focused {
  border-color: #40a9ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

/* react-datepicker样式 */
.probation-regularization-modal .form-control {
  width: 100% !important;
  height: 36px !important;
  padding: 4px 8px !important;
  font-size: 14px !important;
  border: 1px solid #d9d9d9 !important;
  border-radius: 4px !important;
  box-sizing: border-box !important;
  background-color: #fff !important;
  transition: all 0.3s ease !important;
}

.probation-regularization-modal .form-control:focus {
  border-color: #40a9ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
  outline: none !important;
}

.probation-regularization-modal .form-control::placeholder {
  color: #bfbfbf !important;
}

/* react-datepicker弹出层样式 */
.probation-regularization-modal .react-datepicker-popper {
  z-index: 1100 !important;
  position: absolute !important;
}

.probation-regularization-modal .react-datepicker {
  font-size: 12px !important;
  border: 1px solid #d9d9d9 !important;
  border-radius: 4px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

.probation-regularization-modal .react-datepicker__header {
  background-color: #f0f0f0 !important;
  border-bottom: 1px solid #d9d9d9 !important;
}

.probation-regularization-modal .react-datepicker__day--selected {
  background-color: #1890ff !important;
  border-radius: 0.3rem !important;
}

.probation-regularization-modal .react-datepicker__day:hover {
  background-color: #e6f7ff !important;
}

/* 文本域样式 */
.probation-regularization-modal .ant-input {
  resize: vertical !important;
  min-height: 36px !important;
}

/* 备注字数提醒样式 */
.probation-regularization-modal .ant-form-item:last-child div[style*="textAlign: 'right'"] {
  margin-top: 4px !important;
  font-size: 12px !important;
  color: #999 !important;
  text-align: right !important;
}

/* 底部按钮样式 */
.probation-regularization-modal .ant-modal-footer {
  padding: 16px 24px !important;
  border-top: 1px solid #f0f0f0 !important;
}

.probation-regularization-modal .ant-modal-footer > div {
  display: flex !important;
  justify-content: flex-end !important;
  gap: 8px !important;
}

.probation-regularization-modal .ant-modal-footer .ant-btn {
  min-width: 80px !important;
  height: 36px !important;
  padding: 0 16px !important;
  font-size: 14px !important;
  border-radius: 4px !important;
}

.probation-regularization-modal .ant-modal-footer .ant-btn-default {
  background-color: #fff !important;
  border-color: #d9d9d9 !important;
  color: rgba(0, 0, 0, 0.65) !important;
}

.probation-regularization-modal .ant-modal-footer .ant-btn-default:hover:not([disabled]) {
  border-color: #40a9ff !important;
  color: #40a9ff !important;
}

.probation-regularization-modal .ant-modal-footer .ant-btn-default[disabled] {
  background-color: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
  color: rgba(0, 0, 0, 0.25) !important;
  cursor: not-allowed !important;
}

.probation-regularization-modal .ant-modal-footer .ant-btn-primary {
  background-color: #1890ff !important;
  border-color: #1890ff !important;
  color: #fff !important;
}

.probation-regularization-modal .ant-modal-footer .ant-btn-primary:hover:not([disabled]) {
  background-color: #40a9ff !important;
  border-color: #40a9ff !important;
}

.probation-regularization-modal .ant-modal-footer .ant-btn-primary[disabled] {
  background-color: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
  color: rgba(0, 0, 0, 0.25) !important;
  cursor: not-allowed !important;
}

/* 加载状态样式 */
.probation-regularization-modal .ant-btn-loading {
  pointer-events: none !important;
}

/* 响应式设计 */
@media (max-width: 1500px) {
  .probation-reminder-modal .ant-modal,
  .probation-reminder-modal .ant-modal-content {
    width: 1200px !important;
  }
}

@media (max-width: 1300px) {
  .probation-reminder-modal .ant-modal,
  .probation-reminder-modal .ant-modal-content {
    width: 95vw !important;
  }
}

@media (max-width: 768px) {
  .probation-reminder-modal .ant-modal,
  .probation-reminder-modal .ant-modal-content {
    width: 95vw !important;
    margin: 0 auto !important;
  }
  
  .probation-regularization-modal .ant-modal,
  .probation-regularization-modal .ant-modal-content {
    width: 95vw !important;
    margin: 0 auto !important;
  }
}

/* 拖动相关样式 */
.draggable-modal .ant-modal {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  margin: 0 !important;
  padding-bottom: 0 !important;
  transition: none !important;
}

.draggable-modal .ant-modal-wrap {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  display: block !important;
  align-items: unset !important;
  justify-content: unset !important;
}

.draggable-modal .ant-modal-header {
  cursor: move !important;
  -webkit-user-select: none !important;
  user-select: none !important;
  background-color: #fafafa !important;
  border-bottom: 1px solid #f0f0f0 !important;
  padding: 16px 24px !important;
}

.draggable-modal .ant-modal-header:hover {
  background-color: #f0f0f0 !important;
}
/* 确保拖动时的视觉反馈 */
.draggable-modal.dragging .ant-modal {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3) !important;
  z-index: 1001 !important;
}

/* 确保弹窗在屏幕中央（非拖动模式） */
.probation-regularization-modal .ant-modal-wrap {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.probation-regularization-modal .ant-modal {
  top: 0 !important;
  margin: 0 auto !important;
  padding-bottom: 0 !important;
}

/* 不同工作性质员工的颜色区分 */
.work-type-正式 {
  background-color: rgba(82, 196, 26, 0.1) !important; /* 绿色背景 */
  border-left: 4px solid #52c41a !important;
}

.work-type-试用 {
  background-color: rgba(250, 173, 20, 0.1) !important; /* 橙色背景 */
  border-left: 4px solid #faad14 !important;
}

.work-type-实习 {
  background-color: rgba(24, 144, 255, 0.1) !important; /* 蓝色背景 */
  border-left: 4px solid #1890ff !important;
}

.work-type-临时 {
  background-color: rgba(245, 34, 45, 0.1) !important; /* 红色背景 */
  border-left: 4px solid #f5222d !important;
}

.work-type-兼职 {
  background-color: rgba(114, 46, 209, 0.1) !important; /* 紫色背景 */
  border-left: 4px solid #722ed1 !important;
}

.work-type-外包 {
  background-color: rgba(19, 194, 194, 0.1) !important; /* 青色背景 */
  border-left: 4px solid #13c2c2 !important;
}

/* 悬停效果 */
.work-type-正式:hover {
  background-color: rgba(82, 196, 26, 0.15) !important;
}

.work-type-试用:hover {
  background-color: rgba(250, 173, 20, 0.15) !important;
}

.work-type-实习:hover {
  background-color: rgba(24, 144, 255, 0.15) !important;
}

.work-type-临时:hover {
  background-color: rgba(245, 34, 45, 0.15) !important;
}

.work-type-兼职:hover {
  background-color: rgba(114, 46, 209, 0.15) !important;
}

.work-type-外包:hover {
  background-color: rgba(19, 194, 194, 0.15) !important;
}
